<template>
  <div class="nsywpd-dialog-container">
    <!-- 页面初始化loading -->
    <div v-if="pageLoading" class="loading-container">
      <t-loading size="large" text="正在加载..." />
    </div>
    <div v-else class="nsywpd-content">
      <!-- <div class="b-bottom">
        <div class="title1">{{ fromPage == 'cyfa' ? '新建纳税义务判断方案' : '纳税义务判断' }}</div>
      </div> -->
      <!-- 已有方案 -->
      <div v-if="faList.length > 0 && fromPage != 'cyfa'" class="mt-24 mb-24">
        <div v-if="showFamc">
          <div class="gray-card mt-16">
            <div class="content-label" style="padding-bottom: 16px; border: none">
              <span class="title2">{{ famc }}</span>
              <t-button style="margin-top: -5px" theme="primary" variant="text" size="large" @click="qhfa"
                >切换方案</t-button
              >
            </div>
          </div>
        </div>
        <div v-else>
          <div class="gray-card mt-16">
            <div class="content-label" style="padding-bottom: 16px; border: none">
              <span class="title2">可选择已有方案进行申报</span>
              <t-button
                v-if="!showYyFa"
                style="margin-top: -5px"
                theme="primary"
                variant="text"
                size="large"
                @click="xzfa"
                >选择方案</t-button
              >
              <template v-if="showYyFa">
                <div v-if="showMoreFa" class="label-box" @click="clickMoreFa">
                  <span style="font-size: 16px; color: #4285f4; vertical-align: middle; cursor: pointer">更多方案</span
                  ><ChevronUpIcon class="label-icon" />
                </div>
                <div v-else class="label-box" @click="clickMoreFa">
                  <span style="font-size: 16px; color: #4285f4; vertical-align: middle; cursor: pointer">更多方案</span
                  ><ChevronDownIcon class="label-icon" />
                </div>
              </template>
            </div>
            <div
              v-if="showYyFa"
              class="gray-card-item-container gray-card-item-container-flex"
              style="max-height: 200px; overflow: auto"
            >
              <!-- 按钮 -->
              <div v-for="(item, index) of showFaList" :key="index" class="item-button-nsryw" @click="clickFa(index)">
                <div class="item-button-nsryw-top">{{ item.famc }}</div>
                <div class="item-button-nsryw-bottom">
                  {{ `类型：${idxFylxDm2Fylx[item.fylx1jDm].label}/${idxFylxDm2Fylx[item.fylx2jDm].label}` }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 支付的费用类型 -->
      <div class="fee-type-section">
        <div class="section-title">支付的费用类型</div>
        <div class="section-subtitle">请您选择支付的费用类型进行税款计算，如有二级费用类型请继续选择</div>

        <!-- 一级类型 -->
        <div class="form-row">
          <div class="form-label">一级类型</div>
          <div class="form-control">
            <t-select
              v-model="modelData.yjlx"
              :options="yjlxOptions"
              placeholder="特许经营、授权经营、加盟费用"
              clearable
              filterable
              :popup-props="{ overlayStyle: { maxHeight: '200px' } }"
              @change="changeYjlx()"
              @clear="clearYjlx()"
              style="width: 70%"
            ></t-select>
          </div>
        </div>

        <!-- 二级类型 -->
        <div v-show="modelData.yjlx" class="form-row ejlx-row">
          <div class="form-label">二级类型</div>
          <div class="form-control">
            <div class="button-group">
              <t-button
                v-for="(item, index) in ejlxList"
                :key="index"
                @click="ejlxClick(index)"
                :variant="item.theme === 'primary' ? 'base' : 'outline'"
                :theme="item.theme === 'primary' ? 'primary' : 'default'"
                class="type-button"
              >
                {{ item.label }}
              </t-button>
            </div>
          </div>
        </div>
      </div>
      <!-- 回答问题 -->
      <div v-show="wtdaxxList.length > 0" class="question-section">
        <div class="section-title">回答问题</div>
        <div class="section-subtitle">请您根据合同及实际情况回答问题</div>

        <div class="question-list">
          <div v-show="item.visible" v-for="(item, index) in wtdaxxList" :key="index" class="question-item">
            <div class="question-title">{{ item.wtms }}</div>
            <t-radio-group @change="wtdaxxChange($event, item)" v-model="item.sxda" class="question-options">
              <t-radio v-for="(daItem, daIndex) in item.damx" :key="daIndex" :value="daItem.dabh" class="question-option">
                {{ daItem.dams }}
              </t-radio>
            </t-radio-group>
          </div>
        </div>

        <div v-if="showPdjgBtn" class="confirm-button-container">
          <t-button @click="showPdjg" :disabled="disabledShowPdjgBtn" :loading="confirmLoading" theme="primary" class="confirm-button">
            确定
          </t-button>
        </div>
      </div>
      <!-- 判断结果 -->
      <div v-show="visibleObj.visiblePdjg" ref="pdjg" class="section">
        <div class="title2--bold">判断结果</div>
        <div class="title-sub">此结果仅供参考，确认后将与合同关联，以便后续使用</div>
        <t-form labelAlign="left">
          <t-row v-if="ywxxList4Render.length > 0" :gutter="16">
            <template v-for="item of ywxxList4Render">
              <!-- 代扣代缴企业所得税 -->
              <t-col v-if="item.zsxmDm == qysdsZsxmDm" :key="item.zsxmDm" :span="ywxxList4Render.length === 4 ? 3 : 4">
                <div class="gray-card mt-16 plr-24 min-height-235">
                  <div class="title-label">
                    <div class="decorate"></div>
                    <span class="title2">代扣代缴企业所得税</span>
                  </div>
                  <t-form-item style="margin-bottom: 16px" label-width="5rem" label="扣缴类型：">
                    <div class="content-text">{{ kjlxCT[item.kjlxDm] ? kjlxCT[item.kjlxDm] : '' }}</div>
                  </t-form-item>
                  <t-form-item label-width="7rem" label="申报所得类型：">
                    <div class="content-text">{{ sbsdlxCT[item.sbsdlxDm] ? sbsdlxCT[item.sbsdlxDm] : '' }}</div>
                  </t-form-item>
                  <t-form-item label-width="8rem" label="企业所得税税率：">
                    <div class="content-text">{{ item.sl == 0.1 ? '减按10%' : `${item.sl * 100}%` }}</div>
                  </t-form-item>
                </div>
              </t-col>
              <!-- 代扣代缴增值税 -->
              <t-col v-if="item.zsxmDm == zzsZsxmDm" :key="item.zsxmDm" :span="ywxxList4Render.length === 4 ? 3 : 4">
                <div class="gray-card mt-16 plr-24 min-height-235">
                  <div class="title-label">
                    <div class="decorate"></div>
                    <span class="title2">代扣代缴增值税</span>
                  </div>
                  <t-form-item label-width="5rem" label="征收品目：">
                    <div class="content-text">{{ item.zspmmc }}</div>
                  </t-form-item>
                  <t-form-item label-width="6rem" label="增值税税率：">
                    <div class="content-text">{{ `${item.sl * 100}%` }}</div>
                  </t-form-item>
                </div>
              </t-col>
              <!-- 印花税 -->
              <t-col v-if="item.zsxmDm == yhsZsxmDm" :key="item.zsxmDm" :span="ywxxList4Render.length === 4 ? 3 : 4">
                <div class="gray-card mt-16 plr-24 min-height-235">
                  <div class="title-label">
                    <div class="decorate"></div>
                    <span class="title2">印花税</span>
                  </div>
                  <t-form-item label-width="6rem" label="印花税项目：">
                    <div class="content-text">{{ item.zspmmc }}</div>
                  </t-form-item>
                  <t-form-item label-width="3rem" label="税率：">
                    <div class="content-text">{{ yhsslDisplay(item.sl) }}</div>
                  </t-form-item>
                </div>
              </t-col>
              <!-- 代扣代缴文化事业建设费 -->
              <t-col
                v-if="item.zsxmDm == whsyjsfZsxmDm"
                :key="item.zsxmDm"
                :span="ywxxList4Render.length === 4 ? 3 : 4"
              >
                <div class="gray-card mt-16 plr-24 min-height-235">
                  <div class="title-label">
                    <div class="decorate"></div>
                    <span class="title2">代扣代缴文化事业建设费</span>
                  </div>
                  <t-form-item label-width="10rem" label="文化事业建设费费率：">
                    <div class="content-text">{{ `${item.sl * 100}%` }}</div>
                  </t-form-item>
                </div>
              </t-col>
              <!-- 房产税 -->
              <t-col v-if="item.zsxmDm == fcsZsxmDm" :key="item.zsxmDm" :span="ywxxList4Render.length === 4 ? 3 : 4">
                <div class="gray-card mt-16 plr-24 min-height-235">
                  <div class="title-label">
                    <div class="decorate"></div>
                    <span class="title2">房产税</span>
                  </div>
                  <t-form-item label-width="3rem" label="税率：">
                    <div class="content-text">依照房产租金收入计算缴纳，税率为{{ `${item.sl * 100}%` }}</div>
                  </t-form-item>
                </div>
              </t-col>
            </template>
          </t-row>
        </t-form>
      </div>
      <!-- “重置”、“下一步” 按钮 -->
      <div v-show="visibleObj.visiblePdjg" class="section">
        <div class="form-section">
          <div class="title2--bold">方案名称</div>
          <div class="mt-16">
            <t-input v-model="modelData.famc" placeholder="请输入方案名称" style="width: 70%" />
          </div>
        </div>
        <div class="form-section mt-24">
          <div class="title2--bold">方案适用范围</div>
          <div class="mt-16 mb-24">
            <t-select v-model="modelData.fasyfw" placeholder="请选择方案适用范围" style="width: 30%">
              <t-option value="0" label="公用" />
              <t-option value="1" label="私用" />
            </t-select>
          </div>
        </div>
        <div class="text-center">
          <t-space>
            <t-button v-if="!showYyFa" @click="reset" variant="outline" theme="primary">重置</t-button>
            <t-button v-if="changeYyfa" @click="bcfa" variant="outline" theme="primary" :loading="saveLoading">另存为新方案</t-button>
            <t-button @click="bcfa" :loading="saveLoading">保存方案</t-button>
            <!-- <t-button v-else @click="sfznjs">下一步</t-button> -->
          </t-space>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { createStore } from '@/pages/index/views/sb/common/mixin';
import { pageMixin } from '@/pages/index/views/dwfhtz/dwzfzhbs/mixin';
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue';
import store from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';
import api from '@/pages/index/api/dwzfzhbs';
import { round } from '@gt/components';

const { storeMixin } = createStore('dwfhtz/dwzfzhbs/form', store);

export default {
  mixins: [pageMixin, storeMixin],
  components: {
    ChevronDownIcon,
    ChevronUpIcon,
  },
  data() {
    return {
      pageLoading: true, // 页面初始化loading状态
      confirmLoading: false, // 确定按钮loading状态
      saveLoading: false, // 保存按钮loading状态
      modelData: {
        yjlx: '',
        ejlx: '',
        famc: '',
        fasyfw: '0', // 方案适用范围，默认为公用
      },
      // 从那个页面跳转而来
      fromPage: this.$route.query ? this.$route.query.fromPage : '',
      // 方案列表
      faList: [],
      // 展示已有方案
      showYyFa: false,
      // 展示更多方案
      showMoreFa: false,
      // 展示方案名称
      showFamc: false,
      // 选中的方案名称
      famc: '',
      // 展示的方案
      showFaList: [],
      // {key:一级类型,value:{key:二级类型,value:问题答案列表}}
      idxLx2Wtda: {},
      // 一级类型下拉
      yjlxOptions: [],
      // {key:一级类型,value:二级类型列表}
      idxYjlx2EjlxList: {},
      // 二级类型下拉
      ejlxList: [],
      // {key:费用类型,value:费用类型对象}
      idxFylxDm2Fylx: {},
      // {key:费用类型,value:引用规则的支付费用类型}
      idxFylxDm2YyFylxDm: {},
      // 问题答案列表
      wtdaxxList: [],
      // {key:问题序号wtbh,value:问题在列表中下标index}
      idxWtbh2Index: {},
      // 对展示条件（zstj）得倒排索引，key为zstj中的问题序号wtbh，value为展示条件依赖wtbh的问题的wtbh，目的是方便找出wtbh的问题答案变化时影响那些问题展示
      idxZstj2Wtbh: {},
      // 业务信息列表
      ywxxList: [],
      // 问题信息（保存问题编号及对应答案）
      wtxx: '',
      // 确定按钮是否展示
      showPdjgBtn: true,
      // 控制问题答案选择后的确定按钮是否可点击
      disabledShowPdjgBtn: true,
      // 改变已有方案的答案
      changeYyfa: false,
      // 企业所得税征收项目代码
      qysdsZsxmDm: '10104',
      // 增值税征收项目代码
      zzsZsxmDm: '10101',
      // 印花税征收项目代码
      yhsZsxmDm: '10111',
      // 房产税征收项目代码
      fcsZsxmDm: '10110',
      // 文化事业建设费征收项目代码
      whsyjsfZsxmDm: '30217',
      // 申报所得类型
      sbsdlxCT: {},
      // 扣缴类型
      kjlxCT: {},
    };
  },
  computed: {
    // 获取扣缴申报编码
    kjsbbm() {
      // 优先使用路由参数，其次使用 store 中的值，最后使用默认值
      return this.$route.query.kjsbbm ||
             this.$store.state['dwfhtz/dwzfzhbs/form']?.kjsbbm ||
             '2'; // 默认为代扣代缴
    },
    // 特殊情况1：零申报，默认政策，不展示卡片
    isLsb() {
      const { ejlx, wtxx } = this.nsywpdInfo;
      // 1. 二级费用类型：贷款利息，答题选择CA
      // 2. 二级费用类型：从属于贷款的费用，答题选择ACA
      const idxEjlx2Wtxx = {
        '005002': 'C21_A79',
        '005003': 'A23_C21_A79',
      };
      return idxEjlx2Wtxx[ejlx] === wtxx;
    },
    ywxxList4Render() {
      return this.ywxxList.filter(({ zsxmDm }) => !(this.isLsb && zsxmDm === this.qysdsZsxmDm));
    },
    // 印花税展示
    yhsslDisplay() {
      return (slList) => {
        if (Array.isArray(slList)) {
          return slList?.length > 0
            ? [...new Set(slList)].reduce((prev, cur) => {
                return `${prev}${prev.length > 0 ? '或' : ''}${round(cur * 1000)}‰`;
              }, '')
            : '‰';
        }
        return `${round(slList * 1000)}‰`;
      };
    },
  },
  watch: {
    // 监听 userInfo 变化，确保数据加载完成后再执行相关逻辑
    '$store.state.zzstz.userInfo': {
      handler(newUserInfo) {
        if (newUserInfo && newUserInfo.djxh) {
          console.log('userInfo loaded:', newUserInfo);
          // 如果需要在 userInfo 加载后执行特定逻辑，可以在这里添加
        }
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    try {
      this.pageLoading = true;

      // 初始化用户信息到store
      const userInfo = window.sessionStorage.getItem('jgxxList');
      if (userInfo) {
        this.$store.commit('zzstz/setUserInfoData', JSON.parse(userInfo));
      }

      // 获取初始化数据
      const djxh = this.$store.state.zzstz.userInfo?.djxh || '';
      const apiList = [
        api.getSchemes({ djxh,kjsbbm: this.kjsbbm, yxbz: 'Y' }),
        api.getZffylxData({}),
        api.getQuestions({ kjsbbm: this.kjsbbm }),
      ];
      const res = await Promise.all(apiList);

      // 方案列表
      const schemes = res[0];
      this.faList = schemes;

      // 费用类型
      const zffylxData = res[0];
      this.setFylx(zffylxData);

      // 问题答案
      const questions = res[1];
      this.idxLx2Wtda = questions;

      // 设置显示的方案
      if (this.faList.length > 0) {
        this.showFaList = this.faList.length <= 3 ? [...this.faList] : [this.faList[0], this.faList[1], this.faList[2]];
      }

      // 申报所得类型
      this.sbsdlxCT = this.formCT.sbsdlxCT;
      // 扣缴类型
      this.kjlxCT = this.formCT.kjlxMap;
      // 隐藏评定结果
      this.visibleObj.visiblePdjg = false;
    } catch (error) {
      console.error('页面初始化失败:', error);
      this.$message.error('页面初始化失败，请刷新重试');
    } finally {
      this.pageLoading = false;
    }
  },
  mounted() {},
  methods: {
    // 选择方案
    xzfa() {
      this.showYyFa = true;
    },
    // 点击更多方案
    clickMoreFa() {
      this.showMoreFa = !this.showMoreFa;
      if (this.faList.length > 0) {
        if (this.showMoreFa) {
          this.showFaList = [...this.faList];
        } else {
          this.showFaList =
            this.faList.length <= 3 ? [...this.faList] : [this.faList[0], this.faList[1], this.faList[2]];
        }
      }
    },
    // 选中已有方案
    clickFa(index) {
      this.showFamc = true;
      this.changeYyfa = false;
      const faxx = this.showFaList[index];
      const { famc, fylx1jDm, fylx2jDm, wtdaxxList, ywxxList, yhxxList, wtxx } = faxx;
      this.famc = famc;
      this.modelData.yjlx = fylx1jDm;
      this.modelData.ejlx = fylx2jDm;
      this.ejlxList = this.idxYjlx2EjlxList[this.modelData.yjlx] ? this.idxYjlx2EjlxList[this.modelData.yjlx] : [];
      this.ejlxClick(undefined, fylx2jDm);

      wtdaxxList.forEach((item) => {
        const { dabh, wtbh } = item;
        const index = this.idxWtbh2Index[wtbh];
        const wtdaxx = this.wtdaxxList[index];
        wtdaxx.sxda = dabh;

        const zWtbhArr = this.idxZstj2Wtbh[wtbh];
        if (zWtbhArr && zWtbhArr.length > 0) {
          for (let i = 0; i < zWtbhArr.length; i++) {
            const zwtbh = zWtbhArr[i];
            const index = this.idxWtbh2Index[zwtbh];
            const wtdaxx = this.wtdaxxList[index];
            const { zstj } = wtdaxx;
            const visible = this.isShowWt(zstj);
            wtdaxx.visible = visible;
          }
        }
      });

      // 展示的问题都有选答案
      this.visibleObj.visiblePdjg = true;
      this.scrollIntoViewByRef('pdjg');
      this.showPdjgBtn = false;
      this.ywxxList = ywxxList;
      this.wtxx = wtxx;

      // 存下纳税人义务判断结果，给税费智能计算页面使用
      const nsywpdjg = { ywxxList, yhxxList };
      this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, {
        nsywpdjg,
        fylx1jDm,
        fylx2jDm,
        fylx2jMc: this.idxFylxDm2Fylx[fylx2jDm].label,
      });
    },
    // 切换方案
    qhfa() {
      this.showFamc = false;
      this.famc = '';
      this.reset();
    },
    // 设置费用类型
    setFylx(zffylxData) {
      zffylxData.forEach((item) => {
        const { YYGZDZFFYLXBM, SJZFFYLXBM, SJZFFYLXMC, XYBZ, YXBZ, ZFFYLXBM, ZFFYLXMC, ZFFYLXDYHFW } = item;
        if (XYBZ === 'Y' && YXBZ === 'Y') {
          // 生成一级费用类型
          const yjlxObj = { label: SJZFFYLXMC, value: SJZFFYLXBM, sjzffylxDm: '' };
          if (!this.idxFylxDm2Fylx[SJZFFYLXBM]) {
            this.yjlxOptions.push(yjlxObj);
            this.idxFylxDm2Fylx[SJZFFYLXBM] = yjlxObj;
          }

          // 生成二级费用类型
          const ejlxObj = {
            theme: 'default',
            label: ZFFYLXMC,
            value: ZFFYLXBM,
            desc: ZFFYLXDYHFW,
            sjzffylxDm: SJZFFYLXBM,
          };
          let ejlxList = this.idxYjlx2EjlxList[SJZFFYLXBM];
          if (!ejlxList) {
            ejlxList = [];
            this.idxYjlx2EjlxList[SJZFFYLXBM] = ejlxList;
          }
          ejlxList.push(ejlxObj);
          this.idxFylxDm2Fylx[ZFFYLXBM] = ejlxObj;

          // 生成引用规则的支付费用类型编码与正常支付费用类型编码的对应关系
          if (YYGZDZFFYLXBM) {
            this.idxFylxDm2YyFylxDm[ZFFYLXBM] = YYGZDZFFYLXBM;
          }
        }
      });
    },
    // 选择一级费用类型
    changeYjlx() {
      this.wtdaxxList = [];
      this.idxWtbh2Index = {};
      this.idxZstj2Wtbh = {};
      this.disabledShowPdjgBtn = true;
      this.changeYyfa = false;
      this.visibleObj.visiblePdjg = false;
      this.ejlxList = this.idxYjlx2EjlxList[this.modelData.yjlx] ? this.idxYjlx2EjlxList[this.modelData.yjlx] : [];
    },
    // 清空一级费用类型
    clearYjlx() {
      this.reset();
    },
    // 选中二级费用；类型
    ejlxClick(currentIndex, ejlx) {
      this.ejlxList.forEach((item, index) => {
        if (currentIndex === index || item.value === ejlx) {
          item.theme = 'primary';
          this.modelData.ejlx = item.value;
        } else {
          item.theme = 'default';
        }
      });

      this.wtdaxxList = [];
      this.idxWtbh2Index = {};
      this.idxZstj2Wtbh = {};
      this.visibleObj.visiblePdjg = false;
      this.disabledShowPdjgBtn = true;
      this.showPdjgBtn = true;

      let list = [];
      if (this.idxLx2Wtda[this.modelData.yjlx]) {
        if (this.idxLx2Wtda[this.modelData.yjlx][this.modelData.ejlx]) {
          list = this.idxLx2Wtda[this.modelData.yjlx][this.modelData.ejlx];
        } else if (this.idxFylxDm2YyFylxDm[this.modelData.ejlx]) {
          const yyFylxDm = this.idxFylxDm2YyFylxDm[this.modelData.ejlx];
          const yySjFylxDm = this.idxFylxDm2Fylx[yyFylxDm]?.sjzffylxDm;

          this.modelData.yjlx = yySjFylxDm;
          this.modelData.ejlx = yyFylxDm;
          this.ejlxList.forEach((item) => {
            item.theme = 'default';
          });

          this.changeYjlx();
          this.ejlxList.forEach((item) => {
            if (item.value === this.modelData.ejlx) {
              item.theme = 'primary';
            } else {
              item.theme = 'default';
            }
          });

          if (this.idxLx2Wtda[yySjFylxDm] && this.idxLx2Wtda[yySjFylxDm][yyFylxDm]) {
            list = this.idxLx2Wtda[yySjFylxDm][yyFylxDm];
          }
        }
      }

      if (list && list.length > 0) {
        // 对数组排序
        list = list.sort((item1, item2) => {
          return item1.sxh - item2.sxh;
        });
        // 数据处理
        list.forEach((item, index) => {
          const { wtbh, zstj } = item;
          this.idxWtbh2Index[wtbh] = index;

          let visible = true;
          if (zstj) {
            visible = false;

            const zWtbhArr = [];
            zstj.replace(/([\d]+)/g, function ($1) {
              zWtbhArr.push(`100${$1}`);
              return $1;
            });

            for (let i = 0; i < zWtbhArr.length; i++) {
              const zWtbh = zWtbhArr[i];

              let arr = this.idxZstj2Wtbh[zWtbh];
              if (!arr) {
                arr = [];
                this.idxZstj2Wtbh[zWtbh] = arr;
              }

              if (arr.indexOf(wtbh) === -1) {
                arr.push(wtbh);
              }
            }
          }

          this.wtdaxxList.push({ ...item, visible });
        });
      }
      this.disabledShowPdjgBtn = true;
      this.changeYyfa = false;
    },
    // 选择问题答案
    wtdaxxChange(value, wtdaxx) {
      const { wtbh } = wtdaxx;
      const zWtbhArr = this.idxZstj2Wtbh[wtbh];
      if (zWtbhArr && zWtbhArr.length > 0) {
        for (let i = 0; i < zWtbhArr.length; i++) {
          const zWtbh = zWtbhArr[i];
          const index = this.idxWtbh2Index[zWtbh];
          const item = this.wtdaxxList[index];
          const { zstj } = item;
          const visible = this.isShowWt(zstj);
          item.visible = visible;
        }
      }

      // 判断展示的问题是否都有选答案
      let flag = true;
      for (let i = 0; i < this.wtdaxxList.length; i++) {
        const item = this.wtdaxxList[i];
        const { visible, sxda } = item;
        if (visible && !sxda) {
          flag = false;
          break;
        }
      }
      // 展示的问题都有选答案，则确定按钮可点击
      this.disabledShowPdjgBtn = !flag;
      // 展示确定按钮
      this.showPdjgBtn = true;
      // 隐藏评定结果
      this.visibleObj.visiblePdjg = false;

      if (this.showFamc) {
        this.changeYyfa = true;
      }
    },
    // 根据展示条件（zstj）判断问题是否展示
    isShowWt(zstj) {
      const wtbhArr = [];
      zstj.replace(/([\d]+)/g, function ($1) {
        wtbhArr.push(`100${$1}`);
        return $1;
      });

      for (let i = 0; i < wtbhArr.length; i++) {
        const wtbh = wtbhArr[i];
        const index = this.idxWtbh2Index[wtbh];

        let da = -1;
        if (index !== undefined) {
          const { sxda } = this.wtdaxxList[index];
          da = sxda || -1;
        } else {
          console.log(this.idxZstj2Wtbh, this.idxWtbh2Index, this.wtdaxxList);
        }

        zstj = zstj.replace(wtbh.substr(-2), `==${da}`);
      }

      zstj = zstj.replace(/([A-Z]+)/g, function ($1) {
        return `'${$1}'`;
      });
      // eslint-disable-next-line no-eval
      return eval(zstj);
    },
    // 选择问题后，“确认”按钮点击事件，主要进行纳税人义务判断
    async showPdjg() {
      try {
        this.confirmLoading = true;
      const djxh = this.$store.state.zzstz.userInfo?.djxh || '';
      // 纳税人义务判断
      let tmda = '';
      this.wtdaxxList.forEach((item) => {
        const { wtbh } = item;
        const { sxda, visible } = item;

        if (visible) {
          tmda = `${tmda + sxda + wtbh.substr(-2)}_`;
        }
      });
      // 去掉最后一个多余的“_”
      if (tmda.length > 1) {
        tmda = tmda.substr(0, tmda.length - 1);
      }
      // 记录问题信息
      this.wtxx = tmda;
      // 二级费用类型为股权转让时，并且问题答案为c，弹出提示弹窗
      if (this.modelData.ejlx === '023001' && this.wtxx === 'C82') {
        this.$gtDialog.info({
          header: '办理提示',
          closeOnOverlayClick: false,
          confirmBtn: '去办理',
          body: '请根据《国家税务总局关于非居民企业间接转让财产企业所得税若干问题的公告》（国家税务总局公告2015年第7号）规定，办理非居民企业间接转让财产事项报告。',
          onConfirm() {
            // 跳转非居民企业间接转让财产事项报告
            window.location.href = `${window.location.origin}/xxbg/view/sdsxgxxbg/#/yyzx/fjmqyjjzrccsxbg`;
          },
        });
        return;
      }
      // 调用纳税义务判定接口
      const res = await this.judgeDuty({ djxh, tmda, fylx2jDm: this.modelData.ejlx, kjsbbm: this.kjsbbm });
      
      const { bizCode, bizMsg, body } = res;

      if (bizCode !== '00') {
        this.$gtDialog.info({
          body: bizMsg,
          closeOnOverlayClick: false,
        });
        return;
      }

      // 解析body字符串为对象
      let bodyData;
      if (typeof body === 'string') {
        try {
          bodyData = JSON.parse(body);
        } catch (error) {
          console.error('解析body数据失败:', error);
          bodyData = {};
        }
      } else {
        bodyData = body || {};
      }

      if (JSON.stringify(bodyData) === '{}') {
        this.$gtDialog.info({
          body: '当前选择无需申报，请确认您的选择是否正确，如选择有误请重新选择！',
          closeOnOverlayClick: false,
        });
        return;
      }

      const { ywxxList } = bodyData;
      if (ywxxList && ywxxList.length > 0) {
        this.ywxxList = ywxxList;
        this.visibleObj.visiblePdjg = true;
        this.showPdjgBtn = false;
        this.scrollIntoViewByRef('pdjg');
      }

      // 保存二级费用类型和问题答案，为后续特殊的带政策、零申报提供判断条件
      this.$store.commit(`${this.storeName}/UPDATE_PAYLOAD`, {
        fylx1jDm: this.modelData.yjlx,
        fylx2jDm: this.modelData.ejlx,
        fylx2jMc: this.idxFylxDm2Fylx[this.modelData.ejlx].label,
        nsywpdInfo: {
          ejlx: this.modelData.ejlx,
          wtxx: this.wtxx,
        },
      });
      } catch (error) {
        console.error('纳税义务判断失败:', error);
        this.$message.error('纳税义务判断失败，请重试');
      } finally {
        this.confirmLoading = false;
      }
    },
    // “重置”按钮点击事件
    reset() {
      this.modelData = {
        yjlx: '',
        ejlx: '',
      };
      this.UPDATE_PAYLOAD({
        nsywpdInfo: {
          ejlx: '',
          wtxx: '',
        },
      });
      this.ejlxList = [];
      this.wtdaxxList = [];
      this.idxWtbh2Index = {};
      this.idxZstj2Wtbh = {};
      this.visibleObj.visiblePdjg = false;
      this.disabledShowPdjgBtn = true;

      this.famc = '';
      this.showFamc = false;
      this.showPdjgBtn = true;
      this.changeYyfa = false;
    },
    // “保存方案”按钮点击事件
    async bcfa() {
      if (!this.modelData.famc) {
        this.$gtDialog.info({
          body: '请输入方案名称',
          closeOnOverlayClick: false,
        });
        return;
      }

      try {
        this.saveLoading = true;

        // 获取当前判断结果中的税率信息
        const taxRates = this.getTaxRatesFromResult();

        // 1、先保存方案，包含方案适用范围参数和税率信息
        const { bizCode, bizMsg } = await api.saveSchemes({
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          wtxx: this.wtxx,
          famc: this.modelData.famc,
          fasyfw: this.modelData.fasyfw || '0', // 方案适用范围，默认为公用
          kjsbbm: this.kjsbbm,
          fylx1jDm: this.modelData.yjlx, // 支付费用一级类型
          fylx2jDm: this.modelData.ejlx, // 支付费用二级类型
          ...taxRates, // 包含各种税率
        });

        console.log('bizCode:', bizCode);
        if ('00' !== bizCode) {
          this.$message.error(bizMsg);
        } else {
          this.$message.success('保存方案成功');
        }
        // // 2、跳转到常用方法
        // this.$router.push({
        //   name: 'dwzfzhbs/znjsq/cyfa',
        // });
      } catch (error) {
        console.error('保存方案失败:', error);
        this.$message.error('保存方案失败，请重试');
      } finally {
        this.saveLoading = false;
      }
    },
    // 获取税率信息的方法
    getTaxRatesFromResult() {
      const taxRates = {
        qysdssl: null, // 企业所得税税率
        zzssl: null,   // 增值税税率
        whsyjsfsl: null, // 文化事业建设费税率
        yhssl: null,   // 印花税税率
        fcssl: null,   // 房产税税率
      };

      // 从业务信息列表中提取税率
      if (this.ywxxList && this.ywxxList.length > 0) {
        this.ywxxList.forEach(item => {
          const { zsxmDm, sl } = item;

          // 企业所得税
          if (zsxmDm === this.qysdsZsxmDm) {
            taxRates.qysdssl = sl === 0.1 ? '10%' : `${(sl * 100)}%`;
          }
          // 增值税
          else if (zsxmDm === this.zzsZsxmDm) {
            taxRates.zzssl = `${(sl * 100)}%`;
          }
          // 印花税
          else if (zsxmDm === this.yhsZsxmDm) {
            if (Array.isArray(sl)) {
              taxRates.yhssl = sl.length > 0 ? `${(sl[0] * 1000)}‰` : '0‰';
            } else {
              taxRates.yhssl = `${(sl * 1000)}‰`;
            }
          }
          // 文化事业建设费
          else if (zsxmDm === this.whsyjsfZsxmDm) {
            taxRates.whsyjsfsl = `${(sl * 100)}%`;
          }
          // 房产税
          else if (zsxmDm === this.fcsZsxmDm) {
            taxRates.fcssl = `${(sl * 100)}%`;
          }
        });
      }

      return taxRates;
    },
    // “税费智能计算”按钮点击事件
    async sfznjs() {
      // 跳转到税费智能计算
      this.$router.push({
        name: 'dwzfzhbs/znjsq/sfznjs',
        query: this.$route.query,
      });
    },
    // 跳转显示
    scrollIntoViewByRef(name) {
      setTimeout(() => {
        this.$refs[name].scrollIntoView({ behavior: 'smooth', block: 'start' });
      }, 0);
    },
  },
};
</script>

<style lang="less" scoped>
/* 弹窗适配样式 */
.nsywpd-dialog-container {
  height: 100%;
  overflow: hidden;
}

/* Loading容器样式 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.nsywpd-content {
  height: 100%;
  padding: 0 20px;
  overflow-x: hidden;
  overflow-y: auto;
}

/* 调整标题样式，减少上下边距 */
.title1 {
  height: 24px;
  margin: 0 0 16px;
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0;
  color: #27282e;
}

/* 调整各种边距，适配弹窗 */
.mt-24 {
  margin-top: 16px !important;
}

.mb-24 {
  margin-bottom: 16px !important;
}

.mt-16 {
  margin-top: 12px !important;
}

.section {
  margin-top: 24px;
  margin-bottom: 24px;
}

.b-bottom {
  padding-bottom: 12px;
  margin-bottom: 16px;
  border-bottom: 1px solid #27282e14;
}

/* 支付的费用类型样式 */
.fee-type-section {
  margin-top: 24px;
  margin-bottom: 32px;
}

.section-title {
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-subtitle {
  margin-bottom: 24px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}

.form-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;

  &.ejlx-row {
    margin-top: 24px; /* 二级类型与一级类型之间的间距 */
  }
}

.form-label {
  width: 80px;
  font-size: 14px;
  line-height: 32px;
  color: #333;
  flex-shrink: 0;
}

.form-control {
  flex: 1;
  min-width: 0;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.type-button {
  margin-right: 0 !important;
}

/* 回答问题样式 */
.question-section {
  margin-top: 32px;
  margin-bottom: 32px;
}

.question-list {
  margin-top: 24px;
}

.question-item {
  padding: 20px;
  margin-bottom: 24px;
  background: #f9fafd;
  border-radius: 6px;

  &:last-child {
    margin-bottom: 0;
  }
}

.question-title {
  margin-bottom: 16px;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  text-align: left; /* 确保问题标题左对齐 */
  word-break: break-word;
  white-space: pre-wrap; /* 支持换行 */
}

.question-options {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start; /* 确保选项左对齐 */
}

.question-option {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  text-align: left; /* 确保选项文本左对齐 */
}

.confirm-button-container {
  padding-top: 24px;
  margin-top: 32px;
  text-align: right;
  border-top: 1px solid #e5e5e9;
}

.confirm-button {
  height: 36px;
  min-width: 88px;
}

/* section 样式已在上面重新定义 */
.title2 {
  &,
  &--bold {
    height: 28px;
    font-size: 16px;
    line-height: 28px;
    letter-spacing: 0;
  }
  &--bold {
    font-weight: 600;
  }
}

/* 表单区域样式 */
.form-section {
  .title2--bold {
    margin-bottom: 0;
  }

  .mt-16 {
    margin-top: 16px;
  }

  .t-input,
  .t-select {
    width: 100%;
    max-width: 400px;
  }
}
.title-sub {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}
.popup-title {
  font-size: 14px;
  font-weight: bold;
}
.content-text {
  line-height: 32px;
  color: #333;
}

.gray-card {
  &.min-height-235 /deep/.t-form__item {
    margin-bottom: 12px;
  }
  margin-bottom: 16px;
  background: #f9fafd;
  border-radius: 6px;

  .content-label {
    position: relative;
    padding: 12px 16px 0;

    .label-box {
      position: absolute;
      top: 16px;
      right: 10px;
      color: #4285f4;
      cursor: pointer;
      .label-icon {
        font-size: 22px;
      }
    }
  }

  .gray-card-item-container {
    margin: 10px;
    .check-item {
      color: #4285f4;
      cursor: pointer;
    }
    .item-button {
      position: relative;
      width: 23%;
      margin-top: 20px;
      margin-right: 21px;
      cursor: pointer;
      background-color: #fff;
      border: #e5e5e9 solid 0.5px;

      .item-button-child {
        display: flex;
        width: 100%;
        padding: 0;
        background-color: rgba(97, 153, 246, 0.3);
        flex-wrap: wrap;
      }
      .item-button-child-all {
        width: 23%;
        padding: 5px 10px;
        margin-right: 21px;
        .item-button-child-all-hover {
          min-height: 62px;
          padding: 5px 10px;
          &:hover {
            color: #4285f4;
            background-color: #fff;
          }
        }
      }
      .item-button-parent {
        min-height: 62px;
        padding: 5px 35px 10px 10px;
        margin-right: 28px;
        &:nth-child(4n) {
          margin-right: 0;
        }
        .item-overlay {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 999;
          width: 100%;
          height: 100%;
          background-color: rgba(97, 153, 246, 0.3);
          border: #4285f4 solid 0.5px;
        }
        .item-button-icon {
          position: absolute;
          top: 8px;
          right: 0;
          font-size: 22px;
          color: #c2c6cb;
        }
      }
    }
  }

  .gray-card-item-container-flex {
    display: flex;
    padding-bottom: 20px;
    padding-left: 18px;
    flex-wrap: wrap;

    .item-button-nsryw {
      width: 31%;
      padding: 10px;
      margin-top: 20px;
      margin-right: 21px;
      cursor: pointer;
      background-color: #fff;
      border: #e5e5e9 solid 0.5px;
      &:nth-child(3n) {
        margin-right: 0;
      }
      .item-button-nsryw-top {
        margin-bottom: 5px;
      }
      .item-button-nsryw-bottom {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.min-height-235 {
  min-height: 180px;
}

/* 判断结果区域的表单项左边距 */
.section /deep/ .t-form-item {
  margin-left: 8px;
}

.title-label {
  padding: 16px 0 10px;
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(39, 40, 46, 0.08);

  .decorate {
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 9px;
    background: #4285f4;
    transform: translate(0, 3px);
  }
  span {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #333;
  }
}

.tdgv-wrapper .t-form-item /deep/.t-form__label {
  padding-right: 0;
  white-space: normal;
}

/* 确保所有 t-form-item 的标签都能正常显示，覆盖全局CSS重置 */
.t-form-item /deep/.t-form__label {
  display: block !important;
  padding-right: 0;
  font-size: 14px;
  line-height: 22px;
  color: #333;
  white-space: normal;
}

/* 针对当前页面的特殊处理，确保标签显示 */
.nsywpd-dialog-container .t-form-item /deep/.t-form__label {
  display: block !important;
  width: auto !important;
  height: auto !important;
  padding-right: 0 !important;
  margin: 0 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: #333 !important;
  white-space: normal !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 更强的选择器，针对所有可能的标签元素 */
.nsywpd-dialog-container label,
.nsywpd-dialog-container .t-form__label,
.nsywpd-dialog-container .t-form-item label,
.nsywpd-dialog-container .t-form-item .t-form__label {
  display: block !important;
  width: auto !important;
  height: auto !important;
  padding-right: 0 !important;
  margin: 0 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: #333 !important;
  white-space: normal !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 针对判断结果区域的t-form-item标签强制显示 */
.section .gray-card .t-form-item .t-form__label,
.gray-card .t-form-item .t-form__label {
  display: inline-block !important;
  width: auto !important;
  height: auto !important;
  padding-right: 8px !important;
  margin: 0 !important;
  font-size: 14px !important;
  font-weight: normal !important;
  line-height: 32px !important;
  color: #333 !important;
  white-space: nowrap !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 确保t-form-item整体布局正确 */
.gray-card .t-form-item {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 12px !important;
}

/* 确保t-form-item的内容区域正确显示 */
.gray-card .t-form-item .t-form__controls {
  flex: 1 !important;
  margin-left: 0 !important;
}

/* 覆盖全局样式中隐藏标签的问题 */
.nsywpd-dialog-container .formItem .t-form__label,
.nsywpd-dialog-container .gray-card .formItem .t-form__label,
.nsywpd-dialog-container .gray-card .t-form__label,
.nsywpd-dialog-container .section .t-form__label {
  color: #333 !important; /* 覆盖透明色 */
  opacity: 1 !important;
  visibility: visible !important;
}

.t-form-item /deep/.t-form__controls {
  margin-bottom: 0;
}

.info-icon {
  margin-left: 8px;
  font-size: 16px;
  color: #ccc;
  vertical-align: middle;
  cursor: pointer;
}
</style>
