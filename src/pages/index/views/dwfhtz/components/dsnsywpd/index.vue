<!-- eslint-disable no-param-reassign -->
<!--
 * @Descripttion: 台账-纳税义务判断
 * @Version: 1.0
 * @Author: system
 * @Date: 2024-10-01 00:00:00
 * @LastEditors:
 * @LastEditTime:
-->
<template>
  <div :style="{ height: '100%' }">
    <SkeletonFrame v-if="loading" />
    <div class="znsbBackGroupDiv adaption-wrap" v-else>
      <search-control-panel
        ref="queryControl"
        class="znsbHeadqueryDiv"
        :config="querySearchConfig"
        :formRules="querySearchConfigOneRules"
        @search="query({ flag: true })"
        :colNum="4"
        @formChange="(v) => (formData = v)"
        @changeSelect="handleSelectChange"
        :LABEL_THRESHOLD="9"
        :labelWidth="'calc(8em + 32px)'"
      />
      <div class="queryBtns" style="display: flex; justify-content: space-between">
        <gt-space size="10px">
          <t-button theme="primary" @click="() => newOrEditRow()"><AddIcon slot="icon" />新增</t-button>
          <t-button theme="primary" @click="delRow"><DeleteIcon slot="icon" />删除</t-button>
          <t-dropdown
            :options="[
              { content: '导出当前页', value: 1, onClick: () => exportExcl() },
              { content: '导出所有页', value: 2, onClick: () => exportExcl('all') },
            ]"
          >
            <t-button variant="outline" theme="primary"> <DownloadIcon slot="icon" /><span>导出</span> </t-button>
          </t-dropdown>
          <QsbButton />
        </gt-space>
      </div>

      <div class="znsbSbBodyDiv">
        <t-table
          ref="tableRef"
          row-key="uuid"
          hover
          :data="tableData"
          :columns="dataColumns"
          height="100%"
          lazyLoad
          :selected-row-keys="selectedRowKeys"
          @select-change="rehandleSelectChange"
          :pagination="pagination"
          @page-change="pageChange"
          :loading="tableLoading"
          :rowClassName="rowClassName"
        >
          <template #xh="{ rowIndex }">{{ (pagination.current - 1) * pagination.pageSize + rowIndex + 1 }}</template>
          <template #fasyfw="{ row }">
            <span>{{ getFasyfwText(row.fasyfw) }}</span>
          </template>
          <template #qysdssl="{ row }">
            <span>{{ row.qysdssl}}</span>
          </template>
          <template #zzssl="{ row }">
            <span>{{ row.zzssl || '6%' }}</span>
          </template>
          <template #whsyjsfsl="{ row }">
            <span>{{ row.whsyjsfsl || '3%' }}</span>
          </template>
          <template #yhssl="{ row }">
            <span>{{ row.yhssl || '0.05%' }}</span>
          </template>
          <template #fcssl="{ row }">
            <span>{{ row.fcssl || '1.2%' }}</span>
          </template>
          <template #operation="{ row }">
            <t-space size="8px">
              <t-link theme="primary" hover="color" @click="newOrEditRow(row)">
                编辑
              </t-link>
              <t-link theme="danger" hover="color" @click="delRow(row)">
                删除
              </t-link>
            </t-space>
          </template>
        </t-table>

      </div>
    </div>
        <!-- 新增/编辑弹窗 -->
        <t-dialog
          :visible.sync="dialogVisible"
          :header="dialogTitle"
          width="900px"
          :on-confirm="handleConfirm"
          :on-cancel="handleCancel"
        >
          <t-form
            ref="formRef"
            :data="formData"
            :rules="formRules"
            label-align="right"
            :label-width="120"
          >
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="支付费用一级类型" name="zffyyjlx">
                  <t-select
                    v-model="formData.zffyyjlx"
                    placeholder="请选择支付费用一级类型"
                    :popup-props="{ overlayStyle: { maxHeight: '200px' } }"
                    filterable
                    style="width: 70%"
                    @change="handleYjlxChange"
                  >
                    <t-option
                      v-for="option in yjlxOptions"
                      :key="option.value"
                      :value="option.value"
                      :label="option.label"
                    />
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="支付费用二级类型" name="zffyejlx">
                  <t-select
                    v-model="formData.zffyejlx"
                    placeholder="请选择支付费用二级类型"
                    :popup-props="{ overlayStyle: { maxHeight: '200px' } }"
                    filterable
                    :disabled="!formData.zffyyjlx"
                  >
                    <t-option
                      v-for="option in ejlxOptions"
                      :key="option.value"
                      :value="option.value"
                      :label="option.label"
                    />
                  </t-select>
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="方案名称" name="famc">
                  <t-input v-model="formData.famc" placeholder="请输入方案名称" style="width: 70%" />
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="方案适用范围" name="fasyfw">
                  <t-select v-model="formData.fasyfw" placeholder="请选择方案适用范围" style="width: 30%">
                    <t-option value="0" label="公用" />
                    <t-option value="1" label="私用" />
                  </t-select>
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="企业所得税判断" name="qysdsslPd">
                  <t-select v-model="formData.qysdsslPd" placeholder="请选择">
                    <t-option value="是" label="是" />
                    <t-option value="否" label="否" />
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="企业所得税税率" name="qysdssl">
                  <t-input v-model="formData.qysdssl" placeholder="请输入税率" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="增值税判断" name="zzsslPd">
                  <t-select v-model="formData.zzsslPd" placeholder="请选择">
                    <t-option value="是" label="是" />
                    <t-option value="否" label="否" />
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="增值税税率" name="zzssl">
                  <t-input v-model="formData.zzssl" placeholder="请输入税率" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="文化事业建设费判断" name="whsyjsfslPd">
                  <t-select v-model="formData.whsyjsfslPd" placeholder="请选择">
                    <t-option value="是" label="是" />
                    <t-option value="否" label="否" />
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="文化事业建设费税率" name="whsyjsfsl">
                  <t-input v-model="formData.whsyjsfsl" placeholder="请输入税率" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="印花税判断" name="yhsslPd">
                  <t-select v-model="formData.yhsslPd" placeholder="请选择">
                    <t-option value="是" label="是" />
                    <t-option value="否" label="否" />
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="印花税税率" name="yhssl">
                  <t-input v-model="formData.yhssl" placeholder="请输入税率" />
                </t-form-item>
              </t-col>
            </t-row>
            <t-row :gutter="16">
              <t-col :span="12">
                <t-form-item label="房产税判断" name="fcsslPd">
                  <t-select v-model="formData.fcsslPd" placeholder="请选择">
                    <t-option value="是" label="是" />
                    <t-option value="否" label="否" />
                  </t-select>
                </t-form-item>
              </t-col>
              <t-col :span="12">
                <t-form-item label="房产税税率" name="fcssl">
                  <t-input v-model="formData.fcssl" placeholder="请输入税率" />
                </t-form-item>
              </t-col>
            </t-row>

          </t-form>
        </t-dialog>

        <!-- 纳税义务判断弹窗 -->
        <t-dialog
          :visible="nsywpdDialogVisible"
          header="纳税义务判断"
          :width="1200"
          :height="800"
          placement="center"
          :footer="false"
          :close-on-overlay-click="false"
          @close="handleNsywpdClose"
        >
          <div style="height: 700px;">
            <NsywpdComponent
              v-if="nsywpdDialogVisible"
              @close="handleNsywpdClose"
              @save="handleNsywpdSave"
            />
          </div>
        </t-dialog>
  </div>
</template>

<script>
import {
  queryDsnsywpdList,
  addDsnsywpd,
  updateDsnsywpd,
  deleteDsnsywpd,
  exportDsnsywpd,
  downloadDsnsywpdTemplate,
  initNsywpdfa,
  getEjlxOptions,
} from '@/pages/index/api/dwfhtz/dsnsywpd.js';

import { numberToPrice } from '@/utils/numberToCurrency';
import { formatDate } from '@/pages/index/views/util/tzzxTools.js';
import QsbButton from '@/pages/index/components/QsbButton';
import SearchControlPanel from '@/pages/index/components/header-search/indexByTz.vue';
import { AddIcon, DeleteIcon, DownloadIcon, FileIcon, UploadIcon } from 'tdesign-icons-vue';
import SkeletonFrame from '@/pages/index/components/SkeletonFrame';
import NsywpdComponent from '@/pages/index/views/dwfhtz/dwzfzhbs/znjsq/nsywpd/index.vue';
import dwzfzhbsStore from '@/pages/index/views/dwfhtz/dwzfzhbs/form.store';

export default {
  name: 'DsnsywpdIndex',
  components: {
    SkeletonFrame,
    QsbButton,
    SearchControlPanel,
    NsywpdComponent,
    DownloadIcon,
    AddIcon,
    DeleteIcon,
    FileIcon,
    UploadIcon,
  },
  data() {
    return {
      loading: true,
      tableLoading: false,
      dialogVisible: false,
      nsywpdDialogVisible: false, // 纳税义务判断弹窗控制
      dialogTitle: '新增',
      isEdit: false,
      currentRow: null,
      fromName: '',
      formData: {},
      selectedRowKeys: [],
      tableData: [],
      files: [],
      dcLoading: false,
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
      },
      querySearchConfig: [
        {
          label: '方案名称',
          key: 'famc',
          type: 'input',
          placeholder: '请输入方案名称',
        },
        {
          label: '方案适用范围',
          key: 'fasyfw',
          type: 'select',
          placeholder: '请选择方案适用范围',
          options: [
            { label: '公用', value: '0' },
            { label: '私用', value: '1' },
          ],
        },
      ],
      querySearchConfigOneRules: {},
      dataColumns: [
        { colKey: 'xh', title: '序号', width: 60, align: 'center' },
        { colKey: 'famc', title: '方案名称', width: 150, ellipsis: true },
        { colKey: 'fasyfw', title: '方案适用范围', width: 150, align: 'center' },
        { colKey: 'zffyyjlx', title: '支付费用一级类型', width: 140, ellipsis: true },
        { colKey: 'zffyejlx', title: '支付费用二级类型', width: 140, ellipsis: true },
        {
          title: '代扣代缴判断结果',
          align: 'center',
          children: [
            { colKey: 'qysdssl', title: '企业所得税税率', width: 120, align: 'center' },
            { colKey: 'zzssl', title: '增值税税率', width: 100, align: 'center' },
            { colKey: 'whsyjsfsl', title: '文化事业建设费税率', width: 150, align: 'center' },
            { colKey: 'yhssl', title: '印花税税率', width: 100, align: 'center' },
            { colKey: 'fcssl', title: '房产税税率', width: 100, align: 'center' },
          ],
        },
        { colKey: 'operation', title: '操作', width: 150, align: 'center', fixed: 'right' },
      ],
      formRules: {
        zffyyjlx: [{ required: true, message: '请输入支付费用一级类型', type: 'error' }],
        zffyejlx: [{ required: true, message: '请输入支付费用二级类型', type: 'error' }],
        famc: [{ required: true, message: '请输入方案名称', type: 'error' }],
        fasyfw: [{ required: true, message: '请选择方案适用范围', type: 'error' }],
      },
      // 支付费用类型选项
      yjlxOptions: [], // 一级类型选项
      ejlxOptions: [], // 二级类型选项
    };
  },
  methods: {
    numberToPrice,
    formatDate,
    handleSelectChange(val, item) {
      // 处理下拉选择变化
      console.log('选择变化:', val, item);
    },

    /**
     * 处理一级类型变化，获取对应的二级类型选项
     */
    async handleYjlxChange(value) {
      if (!value) {
        this.ejlxOptions = [];
        this.formData.zffyejlx = '';
        return;
      }

      try {
        const result = await getEjlxOptions({ yjlxDm: value });
        if (result.success) {
          this.ejlxOptions = result.data || [];
        } else {
          this.ejlxOptions = [];
          console.warn('获取二级类型选项失败:', result.message);
        }
        // 清空二级类型选择
        this.formData.zffyejlx = '';
      } catch (error) {
        console.error('获取二级类型选项失败:', error);
        this.ejlxOptions = [];
        this.formData.zffyejlx = '';
      }
    },
    rowClassName() {
      // 可以根据需要添加行样式
      return '';
    },
    getFasyfwText(value) {
      const map = {
        '0': '公用',
        '1': '私用',
      };
      return map[value] || '';
    },

    async query(pm = { flag: false }) {
      const { flag } = pm;
      if (flag) this.pagination.current = 1;
      this.tableLoading = true;

      let params = {
        djxh: this.$store.state.zzstz.userInfo?.djxh || '',
        pageNo: this.pagination.current,
        pageSize: this.pagination.pageSize,
      };
      params = {
        ...this.formData,
        ...params,
      };

      try {
        const { data } = await queryDsnsywpdList(params);
        // 处理数据字段映射
        this.tableData = (data.records || []).map(item => ({
          ...item,
          fasyfw: item.syfw, // 将API返回的syfw映射为前端使用的fasyfw
          zffyyjlx: item.zffylxbm || item.zffyyjlx, // 支付费用一级类型
          zffyejlx: item.sjzffylxbm || item.zffyejlx, // 支付费用二级类型
        }));
        this.pagination.total = data.total;
      } catch (e) {
        console.error(e);
        this.tableData = [];
        this.pagination.total = 0;
      } finally {
        this.tableLoading = false;
        if (this.loading) {
          this.loading = false;
        }
      }
    },
    newOrEditRow(row) {
      console.log('row', row);
      console.log('row type:', typeof row);
      console.log('row has uuid:', row && row.uuid);

      // 检查 row 是否是真正的数据对象（有 uuid 字段的数据行）
      if (row && typeof row === 'object' && row.uuid && typeof row.uuid === 'string') {
        // 编辑操作：打开编辑弹窗
        this.isEdit = true;
        this.dialogTitle = '编辑方案';
        this.currentRow = row;
        this.formData = { ...row };
        this.dialogVisible = true;
      } else {
        // 新增操作：打开纳税义务判断弹窗
        console.log('打开纳税义务判断弹窗');
        this.nsywpdDialogVisible = true;
      }
    },
    delRow(row) {
      if (row) {
        const confirmDia = this.$dialog({
          header: '警示',
          body: '请确认是否删除该明细',
          onConfirm: async () => {
            try {
              await deleteDsnsywpd([row.uuid]);
              this.$message.success('删除成功');
              this.query();
            } catch (error) {
              console.error('删除失败:', error);
              this.$message.error('删除失败');
            } finally {
              confirmDia.hide();
            }
          },
          onClose: () => {
            confirmDia.hide();
          },
        });
      } else if (this.selectedRowKeys.length > 0) {
        const confirmDia = this.$dialog({
          header: '警示',
          body: `请确认是否删除选中的${this.selectedRowKeys.length}条记录`,
          onConfirm: async () => {
            try {
              await deleteDsnsywpd(this.selectedRowKeys);
              this.$message.success(`成功删除${this.selectedRowKeys.length}条记录`);
              this.selectedRowKeys = [];
              this.query();
            } catch (error) {
              console.error('批量删除失败:', error);
              this.$message.error('批量删除失败');
            } finally {
              confirmDia.hide();
            }
          },
          onClose: () => {
            confirmDia.hide();
          },
        });
      } else {
        this.$message.warning('请选择要删除的记录');
      }
    },
    async handleConfirm() {
      try {
        this.confirmLoading = true; // 开始loading
        const result = await this.$refs.formRef.validate();
        if (result === true) {
          // 确保包含方案适用范围参数和税率信息
          const saveData = {
            ...this.formData,
            fasyfw: this.formData.fasyfw || '0', // 确保有默认值
            // 支付费用类型信息
            zffyyjlx: this.formData.zffyyjlx, // 支付费用一级类型
            zffyejlx: this.formData.zffyejlx, // 支付费用二级类型
            // 税率信息
            qysdssl: this.formData.qysdssl, // 企业所得税税率
            zzssl: this.formData.zzssl,     // 增值税税率
            whsyjsfsl: this.formData.whsyjsfsl, // 文化事业建设费税率
            yhssl: this.formData.yhssl,     // 印花税税率
            fcssl: this.formData.fcssl,     // 房产税税率
          };

          if (this.isEdit) {
            await updateDsnsywpd(saveData);
          } else {
            await addDsnsywpd(saveData);
          }

          const action = this.isEdit ? '更新' : '新增';
          this.$message.success(`${action}成功`);

          this.dialogVisible = false;
          this.query();
        }
      } catch (error) {
        console.error('保存失败:', error);
        this.$message.error('保存失败');
      } finally {
        this.confirmLoading = false; // 结束loading
      }
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    async downloadTemplate() {
      try {
        await downloadDsnsywpdTemplate();
        this.$message.success('模板下载成功');
      } catch (error) {
        console.error('模板下载失败:', error);
        this.$message.error('模板下载失败');
      }
    },
    handleSuccess(response) {
      console.log('上传成功:', response);
      this.$message.success('导入成功');
      this.files = [];
      this.query();
    },
    handleFail(error) {
      console.log('上传失败:', error);
      this.$message.error('上传失败，请检查文件格式');
    },
    async exportExcl(isAll) {
      if (!this.tableData.length) {
        this.$message.warning('表格数据为空，请查询后重试');
        return;
      }

      this.dcLoading = true;

      try {
        const djParam = {
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          nsrsbh: this.$store.state.zzstz.userInfo?.nsrsbh || '',
          nsrmc: this.$store.state.zzstz.userInfo?.jgmc || '',
          pageNo: this.pagination.current,
          pageSize: this.pagination.pageSize,
        };
        const params = {
          ...this.formData,
          ...djParam,
        };
        if (isAll === 'all') {
          params.pageNo = 1;
          params.pageSize = 1000000;
        }

        await exportDsnsywpd(params);
        this.$message.success('导出成功');

      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      } finally {
        this.dcLoading = false;
      }
    },
    rehandleSelectChange(value) {
      this.selectedRowKeys = value;
    },
    pageChange({ current, pageSize }) {
      this.pagination.current = pageSize !== this.pagination.pageSize ? 1 : current;
      this.pagination.pageSize = pageSize;
      this.query();
    },
    async init() {
      try {
        // 初始化纳税义务判断方案数据
        // await this.initNsywpdfaData();
        // 初始化页面数据
        this.query();
      } catch (error) {
        console.error('初始化失败:', error);
        this.$message.error('初始化失败，请刷新重试');
        // 如果初始化失败，仍然尝试查询数据
        this.query();
      }
    },

    /**
     * 初始化纳税义务判断方案数据
     */
    async initNsywpdfaData() {
      try {
        const djxh = this.$store.state.zzstz.userInfo?.djxh || '';
        const kjsbbm = this.$route.query.kjsbbm || '';

        const params = {
          djxh,
          kjsbbm,
        };

        const result = await initNsywpdfa(params);

        if (result.success) {
          console.log('纳税义务判断方案初始化成功:', result.data);
          // 可以在这里处理初始化返回的数据，比如设置默认选项等
          if (result.data.yjlxOptions) {
            this.yjlxOptions = result.data.yjlxOptions;
          }
        } else {
          console.warn('纳税义务判断方案初始化返回失败:', result.message);
        }
      } catch (error) {
        console.error('调用initNsywpdfa失败:', error);
        throw error;
      }
    },
    // 处理纳税义务判断弹窗关闭
    handleNsywpdClose() {
      this.nsywpdDialogVisible = false;
    },
    // 处理纳税义务判断保存
    handleNsywpdSave(data) {
      console.log('纳税义务判断保存数据:', data);
      this.$message.success('纳税义务判断方案保存成功');
      this.nsywpdDialogVisible = false;
      // 刷新表格数据
      this.query();
    },
  },
  created() {
    // 注册 dwzfzhbs store 模块
    try {
      // 确保父模块存在
      if (!this.$store.hasModule(['dwfhtz'])) {
        this.$store.registerModule(['dwfhtz'], {
          namespaced: true,
          modules: {}
        });
      }
      if (!this.$store.hasModule(['dwfhtz', 'dwzfzhbs'])) {
        this.$store.registerModule(['dwfhtz', 'dwzfzhbs'], {
          namespaced: true,
          modules: {}
        });
      }
      if (!this.$store.hasModule(['dwfhtz', 'dwzfzhbs', 'form'])) {
        this.$store.registerModule(['dwfhtz', 'dwzfzhbs', 'form'], dwzfzhbsStore);
      }
    } catch (error) {
      console.error('注册 store 模块失败:', error);
    }
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    // 取消注册 store 模块（可选，避免内存泄漏）
    try {
      if (this.$store.hasModule(['dwfhtz', 'dwzfzhbs', 'form'])) {
        this.$store.unregisterModule(['dwfhtz', 'dwzfzhbs', 'form']);
      }
      // 注意：不要取消注册父模块，因为可能有其他组件在使用
    } catch (error) {
      console.error('取消注册 store 模块失败:', error);
    }
  },
};
</script>

<style scoped lang="less">
@import '../../../../styles/dialog.less';
@import '../../../../styles/sbPageGy.less';

/deep/ .t-table__footer > tr:first-child > td {
  text-align: right !important;
}
/deep/ .t-table:not(.t-table--striped) .t-table__footer > tr {
  background: #fff !important;
}
.tableButton {
  color: #0052d9 !important;
}
/deep/.t-button--variant-text {
  height: 22px !important;
  padding: 0 !important;
}

/deep/.active-row > td {
  background: #d8e6fa !important;
}
.dialogMiddleCss {
  /deep/ .t-dialog--default {
    width: 750px !important;
    height: 30% !important;
  }
  /deep/ .t-dialog__body .nr {
    height: 80% !important;
  }
}

// 税率单元格样式
.tax-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 4px;

  .tax-result {
    min-width: 24px;
    padding: 2px 6px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    border-radius: 3px;

    &.result-yes {
      color: #00a870;
      background-color: #e8f5f1;
    }

    &.result-no {
      color: #e34d59;
      background-color: #fdf2f3;
    }
  }

  .tax-rate {
    font-size: 11px;
    font-weight: normal;
    color: #666;
  }
}
</style>
