/**
 * 代扣纳税义务判断台账相关API接口
 * <AUTHOR>
 * @date 2024-10-01
 */

import { fetch } from '@/core/request';

const servicePrefix = '/dwfhtz/v1';

/**
 * 查询代扣纳税义务判断台账列表
 * @param {Object} params 查询参数
 * @param {string} params.djxh 登记序号
 * @param {number} params.pageNo 页码
 * @param {number} params.pageSize 每页大小
 * @param {string} params.famc 方案名称
 * @param {string} params.fasyfw 方案适用范围 0-公用 1-私用
 * @param {string} params.zffyyjlx 支付费用一级类型
 * @param {string} params.zffyejlx 支付费用二级类型
 * @returns {Promise}
 */
export function queryDsnsywpdList(params) {
  return fetch({
    url: `${servicePrefix}/queryDsnsywpdList`,
    method: 'post',
    data: params,
    loading: true,
  });
}

/**
 * 查询代扣纳税义务判断台账合计数据
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function queryDsnsywpdSum(params) {
  return fetch({
    url: `${servicePrefix}/querySum`,
    method: 'post',
    data: params,
    loading: true,
  });
}

/**
 * 新增代扣纳税义务判断方案
 * @param {Object} data 方案数据
 * @param {string} data.djxh 登记序号
 * @param {string} data.famc 方案名称
 * @param {string} data.fasyfw 方案适用范围 0-公用 1-私用
 * @param {string} data.zffyyjlx 支付费用一级类型
 * @param {string} data.zffyejlx 支付费用二级类型
 * @param {string} data.qysdsslPd 企业所得税判断 是/否
 * @param {string} data.qysdssl 企业所得税税率
 * @param {string} data.zzsslPd 增值税判断 是/否
 * @param {string} data.zzssl 增值税税率
 * @param {string} data.whsyjsfslPd 文化事业建设费判断 是/否
 * @param {string} data.whsyjsfsl 文化事业建设费税率
 * @param {string} data.yhsslPd 印花税判断 是/否
 * @param {string} data.yhssl 印花税税率
 * @param {string} data.fcsslPd 房产税判断 是/否
 * @param {string} data.fcssl 房产税税率
 * @returns {Promise}
 */
export function addDsnsywpd(data) {
  return fetch({
    url: `${servicePrefix}/add`,
    method: 'post',
    data,
    loading: true,
  });
}

/**
 * 更新代扣纳税义务判断方案
 * @param {Object} data 方案数据
 * @returns {Promise}
 */
export function updateDsnsywpd(data) {
  return fetch({
    url: `${servicePrefix}/update`,
    method: 'post',
    data,
    loading: true,
  });
}

/**
 * 删除代扣纳税义务判断方案
 * @param {Array<string>} uuids 方案UUID数组
 * @returns {Promise}
 */
export function deleteDsnsywpd(uuids) {
  return fetch({
    url: `${servicePrefix}/delete`,
    method: 'post',
    data: { uuids },
    loading: true,
  });
}

/**
 * 导出代扣纳税义务判断台账
 * @param {Object} params 导出参数
 * @returns {Promise}
 */
export function exportDsnsywpd(params) {
  return fetch({
    url: `${servicePrefix}/export`,
    method: 'post',
    data: params,
    loading: true,
    responseType: 'blob',
  });
}

/**
 * 下载代扣纳税义务判断导入模板
 * @returns {Promise}
 */
export function downloadDsnsywpdTemplate() {
  return fetch({
    url: `${servicePrefix}/downloadTemplate`,
    method: 'get',
    loading: true,
    responseType: 'blob',
  });
}

/**
 * 导入代扣纳税义务判断数据
 * @param {FormData} formData 包含文件的表单数据
 * @returns {Promise}
 */
export function importDsnsywpd(formData) {
  return fetch({
    url: `${servicePrefix}/import`,
    method: 'post',
    data: formData,
    loading: true,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 初始化纳税义务判断方案数据
 * @param {Object} params 初始化参数
 * @param {string} params.djxh 登记序号
 * @param {string} params.kjsbbm 扣缴税款编码
 * @returns {Promise}
 */
export function initNsywpdfa(params) {
  return fetch({
    url: `${servicePrefix}/initNsywpdfa`,
    method: 'post',
    data: params,
    loading: true,
  });
}

/**
 * 获取支付费用类型选项
 * @returns {Promise}
 */
export function getZffylxOptions() {
  return fetch({
    url: `${servicePrefix}/getZffylxOptions`,
    method: 'post',
    loading: true,
  });
}

/**
 * 根据一级类型获取二级类型选项
 * @param {Object} params 查询参数
 * @param {string} params.yjlxDm 一级类型代码
 * @returns {Promise}
 */
export function getEjlxOptions(params) {
  return fetch({
    url: `${servicePrefix}/getEjlxOptions`,
    method: 'post',
    data: params,
    loading: true,
  });
}

/**
 * 执行纳税义务判断
 * @param {Object} params 判断参数
 * @param {string} params.djxh 登记序号
 * @param {string} params.kjsbbm 扣缴税款编码
 * @param {string} params.fylx2jDm 二级费用类型代码
 * @param {string} params.wtxx 问题信息
 * @returns {Promise}
 */
export function executeNsywpd(params) {
  return fetch({
    url: `${servicePrefix}/executeNsywpd`,
    method: 'post',
    data: params,
    loading: true,
  });
}

/**
 * 获取纳税义务判断历史记录
 * @param {Object} params 查询参数
 * @param {string} params.djxh 登记序号
 * @param {number} params.pageNo 页码
 * @param {number} params.pageSize 每页大小
 * @returns {Promise}
 */
export function getNsywpdHistory(params) {
  return fetch({
    url: `${servicePrefix}/getHistory`,
    method: 'post',
    data: params,
    loading: true,
  });
}

/**
 * 批量保存纳税义务判断结果
 * @param {Object} data 批量保存数据
 * @param {Array} data.resultList 结果列表
 * @returns {Promise}
 */
export function batchSaveNsywpdResult(data) {
  return fetch({
    url: `${servicePrefix}/batchSaveResult`,
    method: 'post',
    data,
    loading: true,
  });
}

export default {
  queryDsnsywpdList,
  queryDsnsywpdSum,
  addDsnsywpd,
  updateDsnsywpd,
  deleteDsnsywpd,
  exportDsnsywpd,
  downloadDsnsywpdTemplate,
  importDsnsywpd,
  initNsywpdfa,
  getZffylxOptions,
  getEjlxOptions,
  executeNsywpd,
  getNsywpdHistory,
  batchSaveNsywpdResult,
};
