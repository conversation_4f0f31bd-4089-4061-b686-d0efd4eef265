# 代扣纳税义务判断台账 API 使用说明

## 概述

`dsnsywpd.js` 文件提供了代扣纳税义务判断台账相关的所有 API 接口，包括数据查询、增删改、导入导出等功能。

## 主要方法

### 1. 初始化方法

#### `initNsywpdfa(params)`

初始化纳税义务判断方案数据

**参数：**

```javascript
{
  djxh: string,    // 登记序号
  kjsbbm: string   // 扣缴税款编码
}
```

**返回：**

```javascript
{
  success: boolean,
  data: {
    yjlxOptions: Array,  // 一级类型选项
    // 其他初始化数据...
  },
  message: string
}
```

**使用示例：**

```javascript
import { initNsywpdfa } from '@/pages/index/api/dwfhtz/dsnsywpd.js';

async function initData() {
  try {
    const result = await initNsywpdfa({
      djxh: '123456789',
      kjsbbm: 'KJSBBM001',
    });

    if (result.success) {
      console.log('初始化成功:', result.data);
    }
  } catch (error) {
    console.error('初始化失败:', error);
  }
}
```

### 2. 查询方法

#### `queryDsnsywpdList(params)`

查询代扣纳税义务判断台账列表

**参数：**

```javascript
{
  djxh: string,        // 登记序号
  pageNo: number,      // 页码
  pageSize: number,    // 每页大小
  famc: string,        // 方案名称（可选）
  fasyfw: string,      // 方案适用范围（可选）0-公用 1-私用
  zffyyjlx: string,    // 支付费用一级类型（可选）
  zffyejlx: string     // 支付费用二级类型（可选）
}
```

**使用示例：**

```javascript
import { queryDsnsywpdList } from '@/pages/index/api/dwfhtz/dsnsywpd.js';

async function queryData() {
  const params = {
    djxh: '123456789',
    pageNo: 1,
    pageSize: 10,
    famc: '技术服务方案',
  };

  try {
    const { data } = await queryDsnsywpdList(params);
    console.log('查询结果:', data.records);
    console.log('总数:', data.total);
  } catch (error) {
    console.error('查询失败:', error);
  }
}
```

**返回数据格式：**

```javascript
{
  "code": 1,
  "data": {
    "records": [
      {
        "syfw": "0",           // 适用范围 0-公用 1-私用
        "zffylxbm": null,      // 支付费用类型编码
        "whsyjsfsl": null,     // 文化事业建设费税率
        "fcssl": null,         // 房产税税率
        "sjzffylxbm": null,    // 实际支付费用类型编码
        "yhssl": null,         // 印花税税率
        "wtxx": "A03_B04",     // 问题信息
        "uuid": "1021e6b1863b43aaa872f3dd92e6fb2e",
        "qysdssl": null,       // 企业所得税税率
        "djxh": 10013101001120017369,
        "kjsbbm": "2",         // 扣缴税款编码
        "zzssl": null,         // 增值税税率
        "famc": "2222"         // 方案名称
      }
    ],
    "total": 3,              // 总记录数
    "size": 10,              // 每页大小
    "current": 1,            // 当前页
    "pages": 1               // 总页数
  },
  "msg": "成功",
  "type": "znsb",
  "hhid": "5vzpz5gnffhhjsfn",
  "traceId": "5vzpz5gnffhhjsfn"
}
```

**字段映射说明：**
由于 API 返回的字段名与前端使用的字段名存在差异，需要进行字段映射：

- `syfw` -> `fasyfw` (方案适用范围)
- `zffylxbm` -> `zffyyjlx` (支付费用一级类型)
- `sjzffylxbm` -> `zffyejlx` (支付费用二级类型)

### 3. 增删改方法

#### `addDsnsywpd(data)`

新增代扣纳税义务判断方案

**参数：**

```javascript
{
  djxh: string,           // 登记序号
  famc: string,           // 方案名称
  fasyfw: string,         // 方案适用范围 0-公用 1-私用
  zffyyjlx: string,       // 支付费用一级类型
  zffyejlx: string,       // 支付费用二级类型
  qysdsslPd: string,      // 企业所得税判断 是/否
  qysdssl: string,        // 企业所得税税率
  zzsslPd: string,        // 增值税判断 是/否
  zzssl: string,          // 增值税税率
  whsyjsfslPd: string,    // 文化事业建设费判断 是/否
  whsyjsfsl: string,      // 文化事业建设费税率
  yhsslPd: string,        // 印花税判断 是/否
  yhssl: string,          // 印花税税率
  fcsslPd: string,        // 房产税判断 是/否
  fcssl: string           // 房产税税率
}
```

#### `updateDsnsywpd(data)`

更新代扣纳税义务判断方案

#### `deleteDsnsywpd(uuids)`

删除代扣纳税义务判断方案

**参数：**

```javascript
uuids: Array<string>  // 方案UUID数组
```

### 4. 辅助方法

#### `getZffylxOptions()`

获取支付费用类型选项

#### `getEjlxOptions(params)`

根据一级类型获取二级类型选项

**参数：**

```javascript
{
  yjlxDm: string; // 一级类型代码
}
```

#### `executeNsywpd(params)`

执行纳税义务判断

**参数：**

```javascript
{
  djxh: string,        // 登记序号
  kjsbbm: string,      // 扣缴税款编码
  fylx2jDm: string,    // 二级费用类型代码
  wtxx: string         // 问题信息
}
```

## 在 Vue 组件中的使用

### 完整示例

```javascript
<template>
  <div>
    <!-- 表单内容 -->
    <t-form ref="formRef" :data="formData">
      <t-form-item label="支付费用一级类型" name="zffyyjlx">
        <t-select
          v-model="formData.zffyyjlx"
          @change="handleYjlxChange"
        >
          <t-option
            v-for="option in yjlxOptions"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </t-select>
      </t-form-item>

      <t-form-item label="支付费用二级类型" name="zffyejlx">
        <t-select v-model="formData.zffyejlx">
          <t-option
            v-for="option in ejlxOptions"
            :key="option.value"
            :value="option.value"
            :label="option.label"
          />
        </t-select>
      </t-form-item>
    </t-form>
  </div>
</template>

<script>
import {
  initNsywpdfa,
  queryDsnsywpdList,
  getEjlxOptions
} from '@/pages/index/api/dwfhtz/dsnsywpd.js';

export default {
  data() {
    return {
      formData: {},
      yjlxOptions: [],
      ejlxOptions: []
    };
  },

  async created() {
    await this.initData();
  },

  methods: {
    async initData() {
      try {
        const result = await initNsywpdfa({
          djxh: this.$store.state.zzstz.userInfo?.djxh || '',
          kjsbbm: this.$route.query.kjsbbm || ''
        });

        if (result.success && result.data.yjlxOptions) {
          this.yjlxOptions = result.data.yjlxOptions;
        }
      } catch (error) {
        console.error('初始化失败:', error);
      }
    },

    async handleYjlxChange(value) {
      if (!value) {
        this.ejlxOptions = [];
        return;
      }

      try {
        const result = await getEjlxOptions({ yjlxDm: value });
        if (result.success) {
          this.ejlxOptions = result.data || [];
        }
      } catch (error) {
        console.error('获取二级类型失败:', error);
      }
    }
  }
};
</script>
```

## 注意事项

1. 所有 API 调用都包含 loading 状态，会自动显示加载动画
2. 错误处理需要在调用方进行 try-catch 处理
3. 分页查询返回的数据结构包含 `list` 和 `total` 字段
4. 删除操作支持批量删除，传入 UUID 数组
5. 导入导出功能使用 blob 类型响应，需要特殊处理

## API 接口地址

所有接口都以 `/dwfhtz/dsnsywpd/v1` 为前缀，具体接口地址请参考 `dsnsywpd.js` 文件中的定义。
