/**
 * 代扣纳税义务判断台账数据映射测试
 * 用于验证API返回数据的字段映射是否正确
 */

/**
 * 模拟API返回的原始数据
 */
const mockApiResponse = {
  "code": 1,
  "data": {
    "records": [
      {
        "syfw": "0",
        "zffylxbm": "001001",
        "whsyjsfsl": null,
        "fcssl": null,
        "sjzffylxbm": "001000",
        "yhssl": 0.300000,
        "wtxx": "A01_B02",
        "uuid": "5d416cdbf1cb458aa57cb0b2de4d2295",
        "qysdssl": 10.000000,
        "sjzffylxmc": "特许经营、授权经营、加盟费用",
        "zffylxmc": "特许经营、授权经营、加盟费用",
        "djxh": 10013101001120017369,
        "kjsbbm": "2",
        "zzssl": null,
        "famc": "1111"
      }
    ],
    "total": 1,
    "size": 10,
    "current": 1,
    "pages": 1
  },
  "msg": "成功",
  "type": "znsb",
  "hhid": "qyrtz4uo871l1b4g",
  "traceId": "qyrtz4uo871l1b4g"
};

/**
 * 数据映射函数（与Vue组件中的逻辑一致）
 */
function mapApiData(data) {
  return (data.records || []).map(item => ({
    ...item,
    fasyfw: item.syfw, // 将API返回的syfw映射为前端使用的fasyfw
    zffyyjlx: item.sjzffylxmc, // 支付费用一级类型显示名称
    zffyejlx: item.zffylxmc, // 支付费用二级类型显示名称
    // 税率字段自动补%
    qysdssl: item.qysdssl ? `${item.qysdssl}%` : null,
    zzssl: item.zzssl ? `${item.zzssl}%` : null,
    whsyjsfsl: item.whsyjsfsl ? `${item.whsyjsfsl}%` : null,
    yhssl: item.yhssl ? `${item.yhssl}%` : null,
    fcssl: item.fcssl ? `${item.fcssl}%` : null,
  }));
}

/**
 * 测试数据映射
 */
export function testDataMapping() {
  console.log('开始测试数据映射...');
  
  const { data } = mockApiResponse;
  const mappedData = mapApiData(data);
  
  console.log('原始数据:', data.records[0]);
  console.log('映射后数据:', mappedData[0]);
  
  // 验证字段映射
  const originalItem = data.records[0];
  const mappedItem = mappedData[0];
  
  console.log('\n=== 字段映射验证 ===');
  
  // 验证方案适用范围映射
  if (mappedItem.fasyfw === originalItem.syfw) {
    console.log('✅ fasyfw 映射正确:', mappedItem.fasyfw);
  } else {
    console.log('❌ fasyfw 映射错误');
  }
  
  // 验证支付费用类型映射
  if (mappedItem.zffyyjlx === originalItem.sjzffylxmc) {
    console.log('✅ zffyyjlx 映射正确:', mappedItem.zffyyjlx);
  } else {
    console.log('❌ zffyyjlx 映射错误');
  }
  
  if (mappedItem.zffyejlx === originalItem.zffylxmc) {
    console.log('✅ zffyejlx 映射正确:', mappedItem.zffyejlx);
  } else {
    console.log('❌ zffyejlx 映射错误');
  }
  
  // 验证税率字段自动补%
  console.log('\n=== 税率字段验证 ===');
  
  if (originalItem.qysdssl && mappedItem.qysdssl === `${originalItem.qysdssl}%`) {
    console.log('✅ qysdssl 自动补% 正确:', mappedItem.qysdssl);
  } else if (!originalItem.qysdssl && mappedItem.qysdssl === null) {
    console.log('✅ qysdssl 为null时处理正确');
  } else {
    console.log('❌ qysdssl 处理错误');
  }
  
  if (originalItem.yhssl && mappedItem.yhssl === `${originalItem.yhssl}%`) {
    console.log('✅ yhssl 自动补% 正确:', mappedItem.yhssl);
  } else {
    console.log('❌ yhssl 处理错误');
  }
  
  if (!originalItem.zzssl && mappedItem.zzssl === null) {
    console.log('✅ zzssl 为null时处理正确');
  } else if (originalItem.zzssl && mappedItem.zzssl === `${originalItem.zzssl}%`) {
    console.log('✅ zzssl 自动补% 正确:', mappedItem.zzssl);
  } else {
    console.log('❌ zzssl 处理错误');
  }
  
  console.log('\n=== 测试完成 ===');
  
  return {
    original: originalItem,
    mapped: mappedItem,
    success: true
  };
}

/**
 * 验证表格显示数据
 */
export function validateTableData(mappedData) {
  console.log('\n=== 表格显示数据验证 ===');
  
  const item = mappedData[0];
  
  // 检查表格需要的关键字段
  const requiredFields = [
    'uuid',
    'famc',
    'fasyfw',
    'zffyyjlx',
    'zffyejlx',
    'qysdssl',
    'zzssl',
    'whsyjsfsl',
    'yhssl',
    'fcssl'
  ];
  
  requiredFields.forEach(field => {
    if (item.hasOwnProperty(field)) {
      console.log(`✅ ${field}:`, item[field]);
    } else {
      console.log(`❌ 缺少字段: ${field}`);
    }
  });
  
  return true;
}

// 如果在浏览器环境中运行，可以直接测试
if (typeof window !== 'undefined') {
  window.testDsnsywpdMapping = testDataMapping;
  window.validateTableData = validateTableData;
  
  // 自动运行测试
  console.log('自动运行数据映射测试...');
  const result = testDataMapping();
  validateTableData([result.mapped]);
}
