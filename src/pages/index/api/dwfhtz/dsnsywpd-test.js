/**
 * 代扣纳税义务判断台账API测试文件
 * 用于验证API返回数据格式的适配
 */

import { queryDsnsywpdList } from './dsnsywpd.js';

/**
 * 测试API返回数据格式适配
 */
export async function testApiDataFormat() {
  console.log('开始测试 queryDsnsywpdList API 数据格式适配...');
  
  const testParams = {
    djxh: '10013101001120017369',
    pageNo: 1,
    pageSize: 10,
  };

  try {
    const response = await queryDsnsywpdList(testParams);
    console.log('API 原始返回数据:', response);
    
    const { data } = response;
    console.log('数据部分:', data);
    
    // 验证数据结构
    if (data && data.records) {
      console.log('✅ 数据结构正确: data.records 存在');
      console.log('记录数量:', data.records.length);
      console.log('总数:', data.total);
      console.log('当前页:', data.current);
      console.log('每页大小:', data.size);
      
      // 验证第一条记录的字段
      if (data.records.length > 0) {
        const firstRecord = data.records[0];
        console.log('第一条记录:', firstRecord);
        
        // 检查关键字段
        const keyFields = ['uuid', 'famc', 'syfw', 'djxh', 'kjsbbm'];
        keyFields.forEach(field => {
          if (firstRecord.hasOwnProperty(field)) {
            console.log(`✅ 字段 ${field} 存在:`, firstRecord[field]);
          } else {
            console.log(`❌ 字段 ${field} 不存在`);
          }
        });
        
        // 模拟前端数据映射处理
        const mappedRecord = {
          ...firstRecord,
          fasyfw: firstRecord.syfw, // 将API返回的syfw映射为前端使用的fasyfw
          zffyyjlx: firstRecord.zffylxbm || firstRecord.zffyyjlx, // 支付费用一级类型
          zffyejlx: firstRecord.sjzffylxbm || firstRecord.zffyejlx, // 支付费用二级类型
        };
        
        console.log('映射后的记录:', mappedRecord);
      }
    } else {
      console.log('❌ 数据结构不正确: data.records 不存在');
    }
    
    return response;
  } catch (error) {
    console.error('❌ API 调用失败:', error);
    throw error;
  }
}

/**
 * 验证预期的API返回格式
 */
export function validateApiResponseFormat(response) {
  const expectedFormat = {
    code: 'number',
    data: {
      records: 'array',
      total: 'number',
      size: 'number',
      current: 'number',
      pages: 'number'
    },
    msg: 'string',
    type: 'string',
    hhid: 'string',
    traceId: 'string'
  };
  
  console.log('验证API返回格式...');
  
  // 验证顶级字段
  if (typeof response.code === 'number') {
    console.log('✅ code 字段类型正确');
  } else {
    console.log('❌ code 字段类型错误');
  }
  
  if (typeof response.msg === 'string') {
    console.log('✅ msg 字段类型正确');
  } else {
    console.log('❌ msg 字段类型错误');
  }
  
  // 验证data字段
  if (response.data && typeof response.data === 'object') {
    console.log('✅ data 字段存在且为对象');
    
    if (Array.isArray(response.data.records)) {
      console.log('✅ data.records 是数组');
    } else {
      console.log('❌ data.records 不是数组');
    }
    
    if (typeof response.data.total === 'number') {
      console.log('✅ data.total 是数字');
    } else {
      console.log('❌ data.total 不是数字');
    }
  } else {
    console.log('❌ data 字段不存在或不是对象');
  }
  
  return true;
}

/**
 * 模拟API返回数据（用于测试）
 */
export const mockApiResponse = {
  "code": 1,
  "data": {
    "records": [
      {
        "syfw": "0",
        "zffylxbm": null,
        "whsyjsfsl": null,
        "fcssl": null,
        "sjzffylxbm": null,
        "yhssl": null,
        "wtxx": "A03_B04",
        "uuid": "1021e6b1863b43aaa872f3dd92e6fb2e",
        "qysdssl": null,
        "djxh": 10013101001120017369,
        "kjsbbm": "2",
        "zzssl": null,
        "famc": "2222"
      },
      {
        "syfw": "0",
        "zffylxbm": null,
        "whsyjsfsl": null,
        "fcssl": null,
        "sjzffylxbm": null,
        "yhssl": null,
        "wtxx": "A01_B02",
        "uuid": "52b4978ea1bd49949b87259d4671b3a0",
        "qysdssl": null,
        "djxh": 10013101001120017369,
        "kjsbbm": "2",
        "zzssl": null,
        "famc": "111"
      },
      {
        "syfw": "1",
        "zffylxbm": null,
        "whsyjsfsl": null,
        "fcssl": null,
        "sjzffylxbm": null,
        "yhssl": null,
        "wtxx": "A01_B02",
        "uuid": "8bd2778785544a1585d9a192d716fa46",
        "qysdssl": null,
        "djxh": 10013101001120017369,
        "kjsbbm": "2",
        "zzssl": null,
        "famc": "3333"
      }
    ],
    "total": 3,
    "size": 10,
    "current": 1,
    "pages": 1
  },
  "msg": "成功",
  "type": "znsb",
  "hhid": "5vzpz5gnffhhjsfn",
  "traceId": "5vzpz5gnffhhjsfn"
};

// 如果在浏览器环境中运行，可以直接测试
if (typeof window !== 'undefined') {
  window.testDsnsywpdApi = testApiDataFormat;
  window.validateApiFormat = validateApiResponseFormat;
  window.mockApiResponse = mockApiResponse;
}
